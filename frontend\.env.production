# ===========================================
# AutoGitPilot Frontend Production Environment
# ===========================================

# Authentication - Clerk (Production Keys)
VITE_CLERK_PUBLISHABLE_KEY=pk_live_your_production_clerk_publishable_key

# API Configuration
VITE_API_BASE_URL=https://api.autogitpilot.com/api
VITE_API_TIMEOUT=30000

# Payment Processing - Razorpay (Production Keys)
VITE_RAZORPAY_KEY_ID=rzp_live_your_production_key

# Application Configuration
VITE_APP_NAME=AutoGitPilot
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Automate your GitHub commits and maintain your contribution streak

# Feature Flags
VITE_ENABLE_PAYMENTS=true
VITE_ENABLE_AI_FEATURES=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG=false

# External Services (Production)
VITE_SENTRY_DSN=https://<EMAIL>/project_id
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
VITE_HOTJAR_ID=

# Social Media & SEO
VITE_SOCIAL_TWITTER=@autogitpilot
VITE_SOCIAL_GITHUB=https://github.com/autogitpilot
VITE_SOCIAL_DISCORD=
VITE_CANONICAL_URL=https://autogitpilot.com

# Performance & Caching
VITE_ENABLE_SW=true
VITE_CACHE_VERSION=1.0.0
