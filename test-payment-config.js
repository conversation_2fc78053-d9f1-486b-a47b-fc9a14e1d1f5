#!/usr/bin/env node

/**
 * Quick test to verify Razorpay configuration
 */

import dotenv from 'dotenv'
import fetch from 'node-fetch'

// Load environment variables
dotenv.config({ path: './backend/.env' })

async function testPaymentConfig() {
  console.log('🧪 Testing Razorpay Payment Configuration\n')

  // Check environment variables
  console.log('📋 Environment Variables:')
  console.log(`RAZORPAY_KEY_ID: ${process.env.RAZORPAY_KEY_ID ? '✅ Set' : '❌ Missing'}`)
  console.log(`RAZORPAY_KEY_SECRET: ${process.env.RAZORPAY_KEY_SECRET ? '✅ Set' : '❌ Missing'}`)
  console.log(`RAZORPAY_WEBHOOK_SECRET: ${process.env.RAZORPAY_WEBHOOK_SECRET ? '✅ Set' : '❌ Missing'}`)
  
  // Test backend API
  console.log('\n🔗 Testing Backend API:')
  try {
    const response = await fetch('http://localhost:5000/health')
    if (response.ok) {
      console.log('✅ Backend is running')
    } else {
      console.log('❌ Backend health check failed')
    }
  } catch (error) {
    console.log('❌ Backend is not accessible:', error.message)
  }

  // Test frontend environment
  console.log('\n🌐 Frontend Configuration:')
  console.log('Frontend should be running on: http://localhost:3001/')
  console.log('Check browser console for any Razorpay script loading errors')
  
  console.log('\n📝 Summary of fixes applied:')
  console.log('1. ✅ Updated frontend/.env with correct Razorpay key')
  console.log('2. ✅ Added Razorpay checkout script to index.html')
  console.log('3. ✅ Updated webhook secret in backend/.env')
  console.log('4. ✅ Restarted both frontend and backend servers')
  
  console.log('\n🚀 Next steps:')
  console.log('1. Open http://localhost:3001/ in your browser')
  console.log('2. Sign in and navigate to the Upgrade page')
  console.log('3. Try to initiate a payment')
  console.log('4. Check browser console for any errors')
  
  console.log('\n🔍 If payment still fails, check:')
  console.log('- Browser console for JavaScript errors')
  console.log('- Network tab for failed API requests')
  console.log('- Backend logs for Razorpay errors')
}

testPaymentConfig().catch(console.error)
