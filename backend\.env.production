# ===========================================
# AutoGitPilot Backend Production Environment
# ===========================================

# Server Configuration
PORT=5000
NODE_ENV=production
HOST=0.0.0.0

# Security (CHANGE THESE IN PRODUCTION!)
JWT_SECRET=CHANGE_THIS_IN_PRODUCTION_32_CHARS_MIN
SESSION_SECRET=CHANGE_THIS_IN_PRODUCTION_32_CHARS_MIN
ENCRYPTION_KEY=CHANGE_THIS_EXACTLY_32_CHARACTERS

# Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/autogitpilot?retryWrites=true&w=majority&maxPoolSize=10&serverSelectionTimeoutMS=5000&socketTimeoutMS=45000

# Redis Configuration
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=
REDIS_DB=0

# Authentication - Clerk (Production Keys)
CLERK_SECRET_KEY=sk_live_your_production_clerk_secret_key
CLERK_PUBLISHABLE_KEY=pk_live_your_production_clerk_publishable_key
CLERK_WEBHOOK_SECRET=whsec_your_production_webhook_secret

# Payment Processing - Razorpay (Production Keys)
RAZORPAY_KEY_ID=rzp_live_your_production_key
RAZORPAY_KEY_SECRET=your_production_razorpay_secret
RAZORPAY_WEBHOOK_SECRET=your_production_webhook_secret

# AI Services (Production Keys)
OPENAI_API_KEY=sk-your_production_openai_api_key
GEMINI_API_KEY=your_production_gemini_api_key

# Email Configuration (Production SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_production_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=AutoGitPilot

# Frontend Configuration
FRONTEND_URL=https://autogitpilot.com
ALLOWED_ORIGINS=https://autogitpilot.com,https://www.autogitpilot.com

# GitHub Integration (Production)
GITHUB_CLIENT_ID=your_production_github_client_id
GITHUB_CLIENT_SECRET=your_production_github_client_secret
GITHUB_WEBHOOK_SECRET=your_production_github_webhook_secret

# Logging Configuration
LOG_LEVEL=warn
LOG_FILE=logs/app.log
LOG_MAX_SIZE=50m
LOG_MAX_FILES=10

# Rate Limiting (Stricter for production)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=/app/uploads/

# Monitoring & Health Checks
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
SENTRY_DSN=https://<EMAIL>/project_id

# Production Optimizations
COMPRESSION_ENABLED=true
TRUST_PROXY=true
CORS_CREDENTIALS=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
