# ===========================================
# AutoGitPilot Frontend Environment Configuration
# ===========================================

# Authentication - Clerk
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here

# API Configuration
VITE_API_BASE_URL=http://localhost:5000/api
VITE_API_TIMEOUT=30000

# Payment Processing - Razorpay
VITE_RAZORPAY_KEY_ID=rzp_test_your_razorpay_key_here

# Application Configuration
VITE_APP_NAME=AutoGitPilot
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Automate your GitHub commits and maintain your contribution streak

# Feature Flags
VITE_ENABLE_PAYMENTS=true
VITE_ENABLE_AI_FEATURES=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG=true

# External Services
VITE_SENTRY_DSN=
VITE_GOOGLE_ANALYTICS_ID=
VITE_HOTJAR_ID=

# Social Media & SEO
VITE_SOCIAL_TWITTER=@autogitpilot
VITE_SOCIAL_GITHUB=https://github.com/autogitpilot
VITE_SOCIAL_DISCORD=
VITE_CANONICAL_URL=http://localhost:3000

# Performance & Caching
VITE_ENABLE_SW=false
VITE_CACHE_VERSION=1.0.0
