{"name": "autogitpilot", "version": "1.0.0", "description": "Automate your GitHub commits and maintain your contribution streak", "main": "index.js", "scripts": {"install:all": "npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "setup": "npm run install:all && npm run setup:env", "setup:env": "echo 'Please copy .env.example files and configure your environment variables'", "clean": "rm -rf frontend/node_modules backend/node_modules frontend/dist"}, "keywords": ["github", "automation", "commits", "streak", "developer-tools"], "author": "AutoGitPilot Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/autogitpilot.git"}, "bugs": {"url": "https://github.com/your-username/autogitpilot/issues"}, "homepage": "https://autogitpilot.com", "dependencies": {"express-mongo-sanitize": "^2.2.0", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "hpp": "^0.2.3", "ioredis": "^5.6.1", "node-cache": "^5.1.2", "pm2": "^6.0.8", "redis": "^5.6.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xss": "^1.0.15"}}